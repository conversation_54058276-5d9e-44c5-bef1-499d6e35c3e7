[package]
name = "conflux"
version = "0.1.0"
edition = "2021"
authors = ["Conflux Team"]
description = "A distributed configuration center built with Rust and Raft consensus"
license = "MIT"

[workspace]
members = [
    ".",
]

[dependencies]
# Async runtime
tokio = { version = "1.0", features = ["full"] }

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
serde_yaml = "0.9"
toml = "0.8.6"

# Error handling
anyhow = "1.0"
thiserror = "2.0"

# Logging and tracing
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter", "json"] }

# Raft consensus
openraft = { version = "0.9", features = ["serde"] }

# Storage
rocksdb = "0.23"

# HTTP server
axum = "0.8"
tower = "0.5"
tower-http = { version = "0.6", features = ["cors", "trace"] }

# Database
sqlx = { version = "0.8.6", features = ["runtime-tokio-rustls", "postgres", "chrono", "uuid"] }

# Authorization
casbin = { version = "2.2", default-features = false, features = ["runtime-tokio", "logging"] }
sqlx-adapter = "1.2"

# Time handling
chrono = { version = "0.4", features = ["serde"] }

# UUID generation
uuid = { version = "1.0", features = ["v4", "serde"] }

# Configuration
config = "0.15"

# Async traits
async-trait = "0.1"

# Concurrent data structures
dashmap = "6.1"

# Cryptography
ring = "0.17"

# HTTP client
reqwest = { version = "0.12", features = ["json"] }

# System utilities
num_cpus = "1.16"

# Binary serialization
bincode = "2.0"

# Cryptographic hashing
sha2 = "0.10"

[dev-dependencies]
tempfile = "3.8"
tokio-test = "0.4"
